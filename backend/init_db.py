#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表和基础数据
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.todo_item import TodoItem
from app.models.achievement import Achievement
from app.models.future_plan import FuturePlan


def init_database():
    """初始化数据库"""
    print("🔧 Initializing database...")
    
    # 创建应用实例
    app = create_app()
    
    with app.app_context():
        try:
            # 删除所有表（如果存在）
            print("📝 Dropping existing tables...")
            db.drop_all()
            
            # 创建所有表
            print("🏗️  Creating database tables...")
            db.create_all()
            
            # 验证表创建
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✅ Created tables: {', '.join(tables)}")
            
            # 创建测试用户（可选）
            create_test_user = input("Create a test user? (y/N): ").lower().strip()
            if create_test_user == 'y':
                create_sample_data()
            
            print("🎉 Database initialization completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            return False


def create_sample_data():
    """创建示例数据"""
    print("📊 Creating sample data...")
    
    try:
        # 创建测试用户
        test_user = User(
            username='testuser',
            email='<EMAIL>'
        )
        test_user.set_password('password123')
        db.session.add(test_user)
        db.session.commit()
        
        print(f"👤 Created test user: {test_user.username} ({test_user.email})")
        
        # 创建示例Todo项
        sample_todos = [
            {
                'title': 'Complete project documentation',
                'description': 'Write comprehensive documentation for the project',
                'status': 'pending',
                'priority': 'high'
            },
            {
                'title': 'Review code changes',
                'description': 'Review and approve pending pull requests',
                'status': 'in_progress',
                'priority': 'medium'
            },
            {
                'title': 'Update dependencies',
                'description': 'Update all project dependencies to latest versions',
                'status': 'completed',
                'priority': 'low'
            }
        ]
        
        for todo_data in sample_todos:
            todo = TodoItem(
                title=todo_data['title'],
                description=todo_data['description'],
                status=todo_data['status'],
                priority=todo_data['priority'],
                user_id=test_user.id
            )
            db.session.add(todo)
        
        # 创建示例成就
        sample_achievements = [
            {
                'title': 'First Project Completion',
                'description': 'Successfully completed the first major project milestone',
                'quantifiable_results': 'Delivered project 2 days ahead of schedule with 95% test coverage',
                'core_skills_json': ['Project Management', 'Python', 'Testing']
            },
            {
                'title': 'Team Leadership',
                'description': 'Led a team of 5 developers in a critical project',
                'quantifiable_results': 'Improved team productivity by 30% and reduced bugs by 50%',
                'core_skills_json': ['Leadership', 'Communication', 'Agile']
            }
        ]
        
        for achievement_data in sample_achievements:
            achievement = Achievement(
                title=achievement_data['title'],
                description=achievement_data['description'],
                quantifiable_results=achievement_data['quantifiable_results'],
                core_skills_json=achievement_data['core_skills_json'],
                user_id=test_user.id
            )
            db.session.add(achievement)
        
        # 创建示例计划
        sample_plans = [
            {
                'title': 'Learn Machine Learning',
                'description': 'Complete a comprehensive machine learning course',
                'goal_type': 'personal',
                'status': 'active'
            },
            {
                'title': 'Build Mobile App',
                'description': 'Develop and launch a mobile application',
                'goal_type': 'professional',
                'status': 'planning'
            }
        ]
        
        for plan_data in sample_plans:
            plan = FuturePlan(
                title=plan_data['title'],
                description=plan_data['description'],
                goal_type=plan_data['goal_type'],
                status=plan_data['status'],
                user_id=test_user.id
            )
            db.session.add(plan)
        
        db.session.commit()
        print("✅ Sample data created successfully!")
        
    except Exception as e:
        print(f"❌ Failed to create sample data: {e}")
        db.session.rollback()


def check_database():
    """检查数据库状态"""
    print("🔍 Checking database status...")
    
    app = create_app()
    
    with app.app_context():
        try:
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if not tables:
                print("⚠️  No tables found in database")
                return False
            
            print(f"📋 Found tables: {', '.join(tables)}")
            
            # 检查每个表的记录数
            for table in tables:
                try:
                    result = db.session.execute(db.text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    print(f"   {table}: {count} records")
                except Exception as e:
                    print(f"   {table}: Error reading ({e})")
            
            return True
            
        except Exception as e:
            print(f"❌ Database check failed: {e}")
            return False


def main():
    """主函数"""
    print("🚀 Database Management Tool")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
    else:
        print("Available commands:")
        print("  init    - Initialize database")
        print("  check   - Check database status")
        print("  reset   - Reset database (init with sample data)")
        command = input("\nEnter command: ").lower().strip()
    
    if command == 'init':
        success = init_database()
    elif command == 'check':
        success = check_database()
    elif command == 'reset':
        print("⚠️  This will delete all existing data!")
        confirm = input("Are you sure? (yes/no): ").lower().strip()
        if confirm == 'yes':
            success = init_database()
            if success:
                create_sample_data()
        else:
            print("❌ Operation cancelled")
            success = False
    else:
        print(f"❌ Unknown command: {command}")
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
