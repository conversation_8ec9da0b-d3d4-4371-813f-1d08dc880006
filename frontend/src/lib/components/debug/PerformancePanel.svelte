<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import { getMemoryUsage, logMemoryUsage } from '$lib/utils/performance';
  import { getPreloadCacheSize, isRoutePreloaded } from '$lib/utils/pagePreloader';

  // Props
  let { 
    visible = $bindable(false),
    position = 'bottom-right' 
  } = $props<{
    visible?: boolean;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  }>();

  // State
  let memoryUsage = $state<any>(null);
  let performanceEntries = $state<PerformanceEntry[]>([]);
  let cacheSize = $state(0);
  let routePreloadStatus = $state<Record<string, boolean>>({});
  let updateInterval: ReturnType<typeof setInterval> | null = null;

  // Routes to check
  const routes = ['/done', '/doing', '/plan', '/anchor'];

  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  function updatePerformanceData() {
    if (!browser) return;

    // Update memory usage
    memoryUsage = getMemoryUsage();

    // Update performance entries
    if (performance.getEntries) {
      performanceEntries = performance.getEntries().slice(-10); // Last 10 entries
    }

    // Update cache size
    cacheSize = getPreloadCacheSize();

    // Update route preload status
    routes.forEach(route => {
      routePreloadStatus[route] = isRoutePreloaded(route);
    });
  }

  function clearPerformanceData() {
    if (!browser) return;
    
    if (performance.clearMarks) {
      performance.clearMarks();
    }
    if (performance.clearMeasures) {
      performance.clearMeasures();
    }
    
    performanceEntries = [];
  }

  function exportPerformanceData() {
    const data = {
      timestamp: new Date().toISOString(),
      memoryUsage,
      performanceEntries: performanceEntries.map(entry => ({
        name: entry.name,
        entryType: entry.entryType,
        startTime: entry.startTime,
        duration: entry.duration
      })),
      cacheSize,
      routePreloadStatus,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-data-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  onMount(() => {
    updatePerformanceData();
    
    // Update every 2 seconds when visible
    if (visible) {
      updateInterval = setInterval(updatePerformanceData, 2000);
    }
  });

  onDestroy(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  // Watch visibility changes
  $effect(() => {
    if (visible) {
      updatePerformanceData();
      updateInterval = setInterval(updatePerformanceData, 2000);
    } else {
      if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
      }
    }
  });
</script>

{#if visible}
  <div class="fixed {positionClasses[position]} z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4 max-w-sm max-h-96 overflow-y-auto">
    <!-- Header -->
    <div class="flex justify-between items-center mb-3">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance</h3>
      <button
        onclick={() => visible = false}
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        aria-label="Close performance panel"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Memory Usage -->
    {#if memoryUsage}
      <div class="mb-4">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Memory Usage</h4>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span>Used:</span>
            <span class="font-mono">{(memoryUsage.usedJSHeapSize / 1024 / 1024).toFixed(1)} MB</span>
          </div>
          <div class="flex justify-between">
            <span>Total:</span>
            <span class="font-mono">{(memoryUsage.totalJSHeapSize / 1024 / 1024).toFixed(1)} MB</span>
          </div>
          <div class="flex justify-between">
            <span>Limit:</span>
            <span class="font-mono">{(memoryUsage.jsHeapSizeLimit / 1024 / 1024).toFixed(1)} MB</span>
          </div>
        </div>
      </div>
    {/if}

    <!-- Cache Status -->
    <div class="mb-4">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cache Status</h4>
      <div class="text-xs">
        <div class="flex justify-between mb-1">
          <span>Preload Cache:</span>
          <span class="font-mono">{cacheSize} items</span>
        </div>
        <div class="space-y-1">
          {#each routes as route}
            <div class="flex justify-between">
              <span>{route}:</span>
              <span class="font-mono {routePreloadStatus[route] ? 'text-green-600' : 'text-red-600'}">
                {routePreloadStatus[route] ? '✓' : '✗'}
              </span>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Recent Performance Entries -->
    <div class="mb-4">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Recent Entries</h4>
      <div class="space-y-1 text-xs max-h-32 overflow-y-auto">
        {#each performanceEntries.slice(-5) as entry}
          <div class="flex justify-between">
            <span class="truncate mr-2">{entry.name}</span>
            <span class="font-mono">{entry.duration?.toFixed(1) || 'N/A'}ms</span>
          </div>
        {/each}
      </div>
    </div>

    <!-- Actions -->
    <div class="flex space-x-2">
      <button
        onclick={clearPerformanceData}
        class="flex-1 px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded"
      >
        Clear
      </button>
      <button
        onclick={exportPerformanceData}
        class="flex-1 px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded"
      >
        Export
      </button>
      <button
        onclick={updatePerformanceData}
        class="flex-1 px-3 py-1 text-xs bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 text-green-700 dark:text-green-300 rounded"
      >
        Refresh
      </button>
    </div>
  </div>
{/if}

<!-- Toggle button when panel is hidden -->
{#if !visible}
  <button
    onclick={() => visible = true}
    class="fixed {positionClasses[position]} z-40 bg-gray-800 hover:bg-gray-700 text-white p-2 rounded-full shadow-lg transition-colors"
    title="Show performance panel"
    aria-label="Show performance panel"
  >
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
  </button>
{/if}
