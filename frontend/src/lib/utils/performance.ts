/**
 * Performance Utilities
 * 
 * This utility provides functions to optimize and measure performance.
 */
import { browser } from '$app/environment';

/**
 * Debounce function to limit how often a function can be called
 * 
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to wait
 * @returns A debounced version of the function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function to limit how often a function can be called
 * 
 * @param func - The function to throttle
 * @param limit - The number of milliseconds to wait between calls
 * @returns A throttled version of the function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastFunc: ReturnType<typeof setTimeout>;
  let lastRan: number;
  
  return function(...args: Parameters<T>): void {
    if (!inThrottle) {
      func(...args);
      lastRan = Date.now();
      inThrottle = true;
      
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func(...args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}

/**
 * Measures the execution time of a function
 * 
 * @param fn - The function to measure
 * @param name - Optional name for logging
 * @returns The result of the function
 */
export function measurePerformance<T>(fn: () => T, name: string = 'Function'): T {
  if (!browser) return fn();
  
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  console.log(`${name} execution time: ${(end - start).toFixed(2)}ms`);
  
  return result;
}

/**
 * Creates a performance mark and measure
 * 
 * @param markName - The name of the mark
 * @param measureName - Optional name for the measure
 */
export function mark(markName: string, measureName?: string): void {
  if (!browser) return;
  
  performance.mark(markName);
  
  if (measureName) {
    try {
      performance.measure(measureName, markName);
    } catch (e) {
      console.error('Error creating performance measure:', e);
    }
  }
}

/**
 * Lazy loads a component
 * 
 * @param loader - A function that returns a promise resolving to a component
 * @returns A function that can be used with Svelte's dynamic imports
 */
export function lazyLoad(loader: () => Promise<any>): () => Promise<any> {
  return () => {
    mark('component-lazy-load-start');
    return loader().then(component => {
      mark('component-lazy-load-end', 'component-lazy-load');
      return component;
    });
  };
}

/**
 * Gets memory usage information (if available)
 *
 * @returns Memory usage information or null if not available
 */
export function getMemoryUsage(): any {
  if (!browser || !('memory' in performance)) {
    return null;
  }
  return (performance as any).memory;
}

/**
 * Logs current memory usage to console
 */
export function logMemoryUsage(): void {
  const memory = getMemoryUsage();
  if (memory) {
    console.log('Memory Usage:', {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
    });
  }
}

/**
 * Monitors Core Web Vitals
 */
export function monitorCoreWebVitals(): void {
  if (!browser) return;

  if ('PerformanceObserver' in window) {
    try {
      // Monitor Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log('LCP:', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Monitor First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          console.log('FID:', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Monitor Cumulative Layout Shift (CLS)
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        });
        console.log('CLS:', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Failed to set up Core Web Vitals monitoring:', error);
    }
  }
}

/**
 * Optimizes images by adding loading="lazy" and proper sizing
 */
export function optimizeImages(): void {
  if (!browser) return;

  const images = document.querySelectorAll('img:not([loading])');
  images.forEach((img) => {
    (img as HTMLImageElement).loading = 'lazy';
  });
}

/**
 * Monitors long tasks that block the main thread
 */
export function monitorLongTasks(): void {
  if (!browser || !('PerformanceObserver' in window)) return;

  try {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        console.warn(`Long task detected: ${entry.duration}ms`, entry);
      });
    });
    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    console.warn('Failed to set up long task monitoring:', error);
  }
}
