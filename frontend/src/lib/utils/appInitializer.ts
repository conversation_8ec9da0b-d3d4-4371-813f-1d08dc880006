/**
 * Application Initializer
 *
 * This utility handles application initialization tasks like preloading assets,
 * setting up performance monitoring, and initializing stores.
 */
import { browser } from '$app/environment';
import { preloadCriticalImages } from './imagePreloader';
import { preloadSvgIcons } from './svgUtils';
import { mark, monitorCoreWebVitals, optimizeImages, monitorLongTasks, logMemoryUsage } from './performance';
import { themeStore } from '$lib/store/themeStore';
import { preloadAllPages } from './pagePreloader';
import { initUserExperience } from './userExperience';
import { initKeyboardShortcuts, registerShortcutGroup } from './keyboardShortcuts';

/**
 * Initialize the application
 * This should be called once when the app starts
 */
export async function initializeApp(): Promise<void> {
  if (!browser) return;

  // Start performance measurement
  mark('app-initialization-start');

  // Initialize performance monitoring
  initializePerformanceMonitoring();

  // Initialize user experience enhancements
  initializeUserExperience();

  // Initialize theme from localStorage
  initializeTheme();

  // Preload critical images
  try {
    await preloadCriticalImages();
  } catch (error) {
    console.error('Failed to preload critical images:', error);
  }

  // Preload SVG icons
  try {
    await preloadSvgIcons();
  } catch (error) {
    console.error('Failed to preload SVG icons:', error);
  }

  // Optimize images on the page
  optimizeImages();

  // Preload all pages for smooth navigation
  try {
    await preloadAllPages();
  } catch (error) {
    console.warn('Failed to preload all pages:', error);
  }

  // Register service worker if available
  registerServiceWorker();

  // Log initial memory usage
  logMemoryUsage();

  // End performance measurement
  mark('app-initialization-end', 'app-initialization');

  console.log('Application initialized');
}

/**
 * Initialize performance monitoring
 */
function initializePerformanceMonitoring(): void {
  // Start monitoring Core Web Vitals
  monitorCoreWebVitals();

  // Monitor long tasks that might block the main thread
  monitorLongTasks();

  // Log memory usage periodically (every 30 seconds)
  setInterval(() => {
    logMemoryUsage();
  }, 30000);
}

/**
 * Initialize user experience enhancements
 */
function initializeUserExperience(): void {
  // Initialize touch gestures, accessibility, and mobile optimizations
  initUserExperience();

  // Initialize keyboard shortcuts
  initKeyboardShortcuts();

  // Register navigation shortcuts
  registerShortcutGroup({
    name: 'navigation',
    shortcuts: [
      {
        key: 'ArrowLeft',
        callback: () => navigateToPage('prev'),
        description: 'Navigate to previous page'
      },
      {
        key: 'ArrowRight',
        callback: () => navigateToPage('next'),
        description: 'Navigate to next page'
      },
      {
        key: '1',
        callback: () => navigateToPage('/done'),
        description: 'Go to Done page'
      },
      {
        key: '2',
        callback: () => navigateToPage('/doing'),
        description: 'Go to Doing page'
      },
      {
        key: '3',
        callback: () => navigateToPage('/plan'),
        description: 'Go to Plan page'
      }
    ]
  });
}

/**
 * Navigate to a page (helper function for shortcuts)
 */
function navigateToPage(target: string | 'prev' | 'next'): void {
  const pageOrder = ['/done', '/doing', '/plan'];
  const currentPath = window.location.pathname;
  const currentIndex = pageOrder.indexOf(currentPath);

  if (target === 'prev' && currentIndex > 0) {
    window.location.href = pageOrder[currentIndex - 1];
  } else if (target === 'next' && currentIndex < pageOrder.length - 1) {
    window.location.href = pageOrder[currentIndex + 1];
  } else if (typeof target === 'string' && target.startsWith('/')) {
    window.location.href = target;
  }
}

/**
 * Initialize theme settings
 */
function initializeTheme(): void {
  // The themeStore automatically initializes from localStorage
  // Subscribe to changes to ensure DOM is updated
  const unsubscribe = themeStore.subscribe((settings) => {
    if (settings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    if (settings.customBackground) {
      document.documentElement.style.setProperty(
        '--custom-background',
        `url(${settings.customBackground})`
      );
      document.documentElement.classList.add('has-custom-bg');
      document.body.classList.add('has-background');

      // 确保背景可见
      document.body.style.backgroundColor = 'transparent';
      document.documentElement.style.backgroundColor = 'transparent';

      // 如果是暗色模式，添加特殊类
      if (settings.darkMode) {
        document.body.classList.add('dark-with-background');
      } else {
        document.body.classList.remove('dark-with-background');
      }
    } else {
      document.documentElement.style.setProperty('--custom-background', 'none');
      document.documentElement.classList.remove('has-custom-bg');
      document.body.classList.remove('has-background');

      // 恢复默认背景色
      document.body.style.backgroundColor = '';
      document.documentElement.style.backgroundColor = '';
    }
  });

  // No need to unsubscribe as we want this to persist for the app lifetime
}

/**
 * Register service worker for offline support and caching
 */
function registerServiceWorker(): void {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
          console.log('ServiceWorker registration successful with scope:', registration.scope);
        })
        .catch(error => {
          console.error('ServiceWorker registration failed:', error);
        });
    });
  }
}
