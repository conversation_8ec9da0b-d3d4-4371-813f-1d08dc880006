/**
 * User Experience Utilities
 * 
 * This utility provides functions to enhance user experience including
 * touch gestures, accessibility improvements, and mobile optimizations.
 */

import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { debounce, throttle } from './performance';

// Touch gesture state
interface TouchState {
  startX: number;
  startY: number;
  startTime: number;
  isSwipe: boolean;
}

let touchState: TouchState | null = null;

/**
 * Initialize touch gestures for navigation
 */
export function initTouchGestures(): () => void {
  if (!browser) return () => {};

  const pageOrder = ['/done', '/doing', '/plan'];
  const SWIPE_THRESHOLD = 50; // Minimum distance for swipe
  const SWIPE_VELOCITY_THRESHOLD = 0.3; // Minimum velocity for swipe
  const MAX_SWIPE_TIME = 300; // Maximum time for swipe gesture

  const handleTouchStart = (e: TouchEvent) => {
    if (e.touches.length !== 1) return;
    
    const touch = e.touches[0];
    touchState = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      isSwipe: false
    };
  };

  const handleTouchMove = throttle((e: TouchEvent) => {
    if (!touchState || e.touches.length !== 1) return;
    
    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - touchState.startX);
    const deltaY = Math.abs(touch.clientY - touchState.startY);
    
    // If horizontal movement is greater than vertical, it might be a swipe
    if (deltaX > deltaY && deltaX > 20) {
      touchState.isSwipe = true;
      // Prevent scrolling during horizontal swipe
      e.preventDefault();
    }
  }, 16); // ~60fps

  const handleTouchEnd = (e: TouchEvent) => {
    if (!touchState) return;
    
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchState.startX;
    const deltaY = touch.clientY - touchState.startY;
    const deltaTime = Date.now() - touchState.startTime;
    
    const distance = Math.abs(deltaX);
    const velocity = distance / deltaTime;
    
    // Check if it's a valid swipe
    if (
      touchState.isSwipe &&
      distance > SWIPE_THRESHOLD &&
      velocity > SWIPE_VELOCITY_THRESHOLD &&
      deltaTime < MAX_SWIPE_TIME &&
      Math.abs(deltaY) < distance / 2 // Ensure it's mostly horizontal
    ) {
      const currentPath = window.location.pathname;
      const currentIndex = pageOrder.indexOf(currentPath);
      
      if (currentIndex !== -1) {
        let targetIndex = -1;
        
        if (deltaX > 0) {
          // Swipe right - go to previous page
          targetIndex = currentIndex - 1;
        } else {
          // Swipe left - go to next page
          targetIndex = currentIndex + 1;
        }
        
        if (targetIndex >= 0 && targetIndex < pageOrder.length) {
          const targetPath = pageOrder[targetIndex];
          goto(targetPath).catch(console.error);
        }
      }
    }
    
    touchState = null;
  };

  // Add event listeners
  document.addEventListener('touchstart', handleTouchStart, { passive: true });
  document.addEventListener('touchmove', handleTouchMove, { passive: false });
  document.addEventListener('touchend', handleTouchEnd, { passive: true });

  // Cleanup function
  return () => {
    document.removeEventListener('touchstart', handleTouchStart);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  };
}

/**
 * Initialize accessibility improvements
 */
export function initAccessibility(): void {
  if (!browser) return;

  // Add focus indicators for keyboard navigation
  const style = document.createElement('style');
  style.textContent = `
    /* Enhanced focus indicators */
    *:focus-visible {
      outline: 2px solid #6366f1 !important;
      outline-offset: 2px !important;
      border-radius: 4px !important;
    }
    
    /* Skip to main content link */
    .skip-to-main {
      position: absolute;
      top: -40px;
      left: 6px;
      background: #6366f1;
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 9999;
      transition: top 0.3s;
    }
    
    .skip-to-main:focus {
      top: 6px;
    }
    
    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  `;
  document.head.appendChild(style);

  // Add skip to main content link
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.className = 'skip-to-main';
  skipLink.textContent = 'Skip to main content';
  document.body.insertBefore(skipLink, document.body.firstChild);

  // Add main content landmark if it doesn't exist
  const main = document.querySelector('main');
  if (main && !main.id) {
    main.id = 'main-content';
  }
}

/**
 * Initialize mobile optimizations
 */
export function initMobileOptimizations(): void {
  if (!browser) return;

  // Prevent zoom on input focus (iOS)
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute('content', 
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
    );
  }

  // Add touch-action CSS for better touch handling
  const style = document.createElement('style');
  style.textContent = `
    /* Optimize touch interactions */
    body {
      touch-action: manipulation;
      -webkit-text-size-adjust: 100%;
    }
    
    /* Improve button touch targets */
    button, a, input, select, textarea {
      min-height: 44px;
      min-width: 44px;
    }
    
    /* Prevent text selection on UI elements */
    .no-select {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    
    /* Smooth scrolling */
    html {
      scroll-behavior: smooth;
    }
    
    /* Hide scrollbars on mobile while keeping functionality */
    @media (max-width: 768px) {
      ::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }
    }
  `;
  document.head.appendChild(style);

  // Add no-select class to navigation elements
  const navElements = document.querySelectorAll('nav, .arrow-button, .page-dropdown-container');
  navElements.forEach(el => el.classList.add('no-select'));
}

/**
 * Initialize error boundaries and user feedback
 */
export function initErrorHandling(): void {
  if (!browser) return;

  // Global error handler
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // You could send this to an error reporting service
  });

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // You could send this to an error reporting service
  });
}

/**
 * Initialize all user experience enhancements
 */
export function initUserExperience(): () => void {
  const cleanupFunctions: (() => void)[] = [];

  // Initialize touch gestures
  cleanupFunctions.push(initTouchGestures());

  // Initialize accessibility improvements
  initAccessibility();

  // Initialize mobile optimizations
  initMobileOptimizations();

  // Initialize error handling
  initErrorHandling();

  // Return cleanup function
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  };
}
