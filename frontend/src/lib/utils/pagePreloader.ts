/**
 * Page Preloader Utility
 * 
 * This utility provides functions to preload page resources and components
 * to improve navigation performance and reduce white screen time.
 */

import { browser } from '$app/environment';
import { preloadRouteImages } from './imagePreloader';
import { preloadRouteSvgIcons } from './svgUtils';

// Cache for preloaded resources
const preloadCache = new Map<string, Promise<any>>();

/**
 * Preloads a page's resources including images, icons, and components
 */
export async function preloadPage(route: string): Promise<void> {
  if (!browser) return;

  // Check if already preloading or preloaded
  if (preloadCache.has(route)) {
    return preloadCache.get(route);
  }

  // Create preload promise
  const preloadPromise = (async () => {
    try {
      // Preload in parallel for better performance
      await Promise.all([
        preloadRouteImages(route),
        preloadRouteSvgIcons(route),
        preloadPageComponent(route)
      ]);
      
      console.log(`Successfully preloaded resources for ${route}`);
    } catch (error) {
      console.warn(`Failed to preload some resources for ${route}:`, error);
    }
  })();

  // Cache the promise
  preloadCache.set(route, preloadPromise);
  
  return preloadPromise;
}

/**
 * Preloads the Svelte component for a specific route
 */
async function preloadPageComponent(route: string): Promise<void> {
  if (!browser) return;

  try {
    // Map routes to their component imports
    const componentMap: Record<string, () => Promise<any>> = {
      '/done': () => import('../../routes/(app)/done/+page.svelte'),
      '/doing': () => import('../../routes/(app)/doing/+page.svelte'),
      '/plan': () => import('../../routes/(app)/plan/+page.svelte'),
      '/anchor': () => import('../../routes/(app)/anchor/+page.svelte')
    };

    const componentLoader = componentMap[route];
    if (componentLoader) {
      await componentLoader();
    }
  } catch (error) {
    console.warn(`Failed to preload component for ${route}:`, error);
  }
}

/**
 * Preloads the next logical page based on current route
 */
export function preloadNextPage(currentRoute: string): Promise<void> {
  const routeOrder = ['/done', '/doing', '/plan'];
  const currentIndex = routeOrder.indexOf(currentRoute);
  
  if (currentIndex !== -1 && currentIndex < routeOrder.length - 1) {
    const nextRoute = routeOrder[currentIndex + 1];
    return preloadPage(nextRoute);
  }
  
  return Promise.resolve();
}

/**
 * Preloads the previous logical page based on current route
 */
export function preloadPreviousPage(currentRoute: string): Promise<void> {
  const routeOrder = ['/done', '/doing', '/plan'];
  const currentIndex = routeOrder.indexOf(currentRoute);
  
  if (currentIndex > 0) {
    const prevRoute = routeOrder[currentIndex - 1];
    return preloadPage(prevRoute);
  }
  
  return Promise.resolve();
}

/**
 * Preloads all main pages for optimal navigation
 */
export async function preloadAllPages(): Promise<void> {
  if (!browser) return;

  const mainRoutes = ['/done', '/doing', '/plan', '/anchor'];
  
  // Preload all pages in parallel
  await Promise.allSettled(
    mainRoutes.map(route => preloadPage(route))
  );
}

/**
 * Clears the preload cache
 */
export function clearPreloadCache(): void {
  preloadCache.clear();
}

/**
 * Gets the size of the preload cache
 */
export function getPreloadCacheSize(): number {
  return preloadCache.size;
}

/**
 * Checks if a route is preloaded
 */
export function isRoutePreloaded(route: string): boolean {
  return preloadCache.has(route);
}
